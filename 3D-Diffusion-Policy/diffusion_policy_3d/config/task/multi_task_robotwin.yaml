name: multi_task_robotwin

shape_meta: &shape_meta
  # acceptable types: rgb, low_dim, text
  obs:
    point_cloud:
      shape: [1024, 6]
      type: point_cloud
    agent_pos:
      shape: [14]
      type: low_dim
    language:
      shape: []
      type: text
  action:
    shape: [14]

env_runner:
  _target_: diffusion_policy_3d.env_runner.robot_runner.RobotRunner
  max_steps: 300
  n_obs_steps: ${n_obs_steps}
  n_action_steps: ${n_action_steps}
  task_name: robot

dataset:
  _target_: diffusion_policy_3d.dataset.multi_task_robotwin_dataset.MultiTaskRobotwinDataset
  multi_task_config:
    language_encoder: "sentence-transformers/all-MiniLM-L6-v2"
    language_mlp_dim: 64
  data_root: "data/multi-task-data"
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 0
  val_ratio: 0.02
  max_train_episodes: 10
