<?xml version="1.0" ?>
<robot name="partnet_68446c00eeed26264c3aee8e9d3a6ffa">
	<link name="base"/>
	<link name="link_0">
		<visual name="vertical_side_panel-32">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-7.obj"/>
			</geometry>
		</visual>
		<visual name="vertical_side_panel-32">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-6.obj"/>
			</geometry>
		</visual>
		<visual name="vertical_side_panel-33">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-4.obj"/>
			</geometry>
		</visual>
		<visual name="vertical_side_panel-33">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-3.obj"/>
			</geometry>
		</visual>
		<visual name="back_panel-34">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-15.obj"/>
			</geometry>
		</visual>
		<visual name="bottom_panel-35">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-10.obj"/>
			</geometry>
		</visual>
		<visual name="bottom_panel-35">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-9.obj"/>
			</geometry>
		</visual>
		<visual name="top_panel-36">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-13.obj"/>
			</geometry>
			<!-- <material name="black">
                <color rgba="0.960784 0.87058824 0.70196078 0.98" />
                <specular rgba="1 1 1 1" /> 添加这行来增加反射光效果 
                <shininess value="50.0" />  添加这行来调整光泽度 
            </material> -->
		</visual>
		<visual name="top_panel-36">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-12.obj"/>
			</geometry>
		</visual>
		<visual name="frame_horizontal_bar-37">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-14.obj"/>
			</geometry>
		</visual>
		<visual name="frame_horizontal_bar-38">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-16.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-7.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-6.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-4.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-3.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-15.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-10.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-9.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-13.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-12.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-14.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-16.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_0" type="fixed">
		<origin rpy="1.570796326794897 0 -1.570796326794897" xyz="0 0 0"/>
		<child link="link_0"/>
		<parent link="base"/>
	</joint>
	<link name="link_1">
		<visual name="drawer_front-25">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-18.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_front-25">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-25.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-26">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-23.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-27">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-24.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_back-28">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-26.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-29">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-21.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-29">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-22.obj"/>
			</geometry>
		</visual>
		<visual name="handle-24">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-30.obj"/>
			</geometry>
		</visual>
		<visual name="handle-24">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-32.obj"/>
			</geometry>
		</visual>
		<visual name="handle-24">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-31.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_rails-30">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-27.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_rails-31">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-28.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-18.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-25.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-23.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-24.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-26.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-21.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-22.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-30.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-32.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-31.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-27.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-28.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_1" type="prismatic">
		<origin xyz="0 0 0"/>
		<axis xyz="0 0 1"/>
		<child link="link_1"/>
		<parent link="link_0"/>
		<limit lower="0" upper="0.6600000000000001"/>
	</joint>
	<link name="link_2">
		<visual name="drawer_front-16">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-34.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_front-16">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-41.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-17">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-40.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-18">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-39.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_back-19">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-42.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-20">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-37.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-20">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-38.obj"/>
			</geometry>
		</visual>
		<visual name="handle-15">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-47.obj"/>
			</geometry>
		</visual>
		<visual name="handle-15">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-46.obj"/>
			</geometry>
		</visual>
		<visual name="handle-15">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-48.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_rails-21">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-43.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_rails-22">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-44.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-34.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-41.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-40.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-39.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-42.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-37.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-38.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-47.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-46.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-48.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-43.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-44.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_2" type="prismatic">
		<origin xyz="0 0 0"/>
		<axis xyz="0 0 1"/>
		<child link="link_2"/>
		<parent link="link_0"/>
		<limit lower="0" upper="0.6600000000000001"/>
	</joint>
	<link name="link_3">
		<visual name="drawer_front-8">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-57.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_front-8">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-50.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-9">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-55.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-9">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-60.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_side-10">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-56.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_back-11">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-58.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-12">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-53.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_bottom-12">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-54.obj"/>
			</geometry>
		</visual>
		<visual name="handle-7">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-62.obj"/>
			</geometry>
		</visual>
		<visual name="handle-7">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-64.obj"/>
			</geometry>
		</visual>
		<visual name="handle-7">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-63.obj"/>
			</geometry>
		</visual>
		<visual name="drawer_rails-13">
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-59.obj"/>
			</geometry>
		</visual>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-57.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-50.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-55.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-60.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-56.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-58.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-53.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-54.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-62.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-64.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-63.obj"/>
			</geometry>
		</collision>
		<collision>
			<origin xyz="0 0 0"/>
			<geometry>
				<mesh filename="textured_objs/original-59.obj"/>
			</geometry>
		</collision>
	</link>
	<joint name="joint_3" type="prismatic">
		<origin xyz="0 0 0"/>
		<axis xyz="0 0 1"/>
		<child link="link_3"/>
		<parent link="link_0"/>
		<limit lower="0" upper="0.6600000000000001"/>
	</joint>
</robot>
