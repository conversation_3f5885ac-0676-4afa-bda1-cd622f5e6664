[{"id": 0, "parent": -1, "joint": "heavy", "name": "cabinet_frame", "parts": [{"id": 32, "name": "vertical_side_panel", "children": []}, {"id": 33, "name": "vertical_side_panel", "children": []}, {"id": 34, "name": "back_panel", "children": []}, {"id": 35, "name": "bottom_panel", "children": []}, {"id": 36, "name": "top_panel", "children": []}, {"id": 37, "name": "frame_horizontal_bar", "children": []}, {"id": 38, "name": "frame_horizontal_bar", "children": []}], "jointData": {}}, {"id": 1, "parent": 0, "joint": "slider", "name": "drawer", "parts": [{"id": 25, "name": "drawer_front", "children": []}, {"id": 26, "name": "drawer_side", "children": []}, {"id": 27, "name": "drawer_side", "children": []}, {"id": 28, "name": "drawer_back", "children": []}, {"id": 29, "name": "drawer_bottom", "children": []}, {"id": 24, "name": "handle", "children": []}, {"id": 30, "name": "drawer_rails", "children": []}, {"id": 31, "name": "drawer_rails", "children": []}], "jointData": {"axis": {"origin": [0, 0, 0], "direction": [0, 0, 1]}, "limit": {"a": 0, "b": 0.6600000000000001, "noLimit": false}}}, {"id": 2, "parent": 0, "joint": "slider", "name": "drawer", "parts": [{"id": 16, "name": "drawer_front", "children": []}, {"id": 17, "name": "drawer_side", "children": []}, {"id": 18, "name": "drawer_side", "children": []}, {"id": 19, "name": "drawer_back", "children": []}, {"id": 20, "name": "drawer_bottom", "children": []}, {"id": 15, "name": "handle", "children": []}, {"id": 21, "name": "drawer_rails", "children": []}, {"id": 22, "name": "drawer_rails", "children": []}], "jointData": {"axis": {"origin": [0, 0, 0], "direction": [0, 0, 1]}, "limit": {"a": 0, "b": 0.6600000000000001, "noLimit": false}}}, {"id": 3, "parent": 0, "joint": "slider", "name": "drawer", "parts": [{"id": 8, "name": "drawer_front", "children": []}, {"id": 9, "name": "drawer_side", "children": []}, {"id": 10, "name": "drawer_side", "children": []}, {"id": 11, "name": "drawer_back", "children": []}, {"id": 12, "name": "drawer_bottom", "children": []}, {"id": 7, "name": "handle", "children": []}, {"id": 13, "name": "drawer_rails", "children": []}], "jointData": {"axis": {"origin": [0, 0, 0], "direction": [0, 0, 1]}, "limit": {"a": 0, "b": 0.6600000000000001, "noLimit": false}}}]